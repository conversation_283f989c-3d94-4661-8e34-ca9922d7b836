<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخة تجريبية - نقطة البيع</title>
    <style>
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 20px; min-height: 100vh; }
        .container { max-width: 400px; margin: 50px auto; background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #333; margin: 0; font-size: 28px; }
        .logo p { color: #666; margin: 5px 0 0 0; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; color: #333; font-weight: bold; }
        .form-group input { width: 100%; padding: 12px; border: 2px solid #ddd; border-radius: 8px; font-size: 16px; box-sizing: border-box; }
        .form-group input:focus { border-color: #007cba; outline: none; }
        .btn { width: 100%; background: #007cba; color: white; padding: 15px; border: none; border-radius: 8px; font-size: 18px; cursor: pointer; margin-top: 10px; }
        .btn:hover { background: #005a87; }
        .demo-info { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .demo-info h3 { margin: 0 0 10px 0; color: #0066cc; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🏪 نقطة البيع</h1>
            <p>نسخة تجريبية</p>
        </div>
        
        <div class="demo-info">
            <h3>🚀 النسخة التجريبية</h3>
            <p>هذه نسخة تجريبية من النظام تعمل بدون قاعدة بيانات لأغراض العرض.</p>
        </div>

        <?php
        session_start();
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            
            // تسجيل دخول تجريبي
            if ($username == 'admin' && $password == 'admin123') {
                $_SESSION['user_id'] = 1;
                $_SESSION['username'] = 'admin';
                $_SESSION['role'] = 'admin';
                
                // توجيه إلى صفحة POS التجريبية
                header("Location: demo_pos.php");
                exit;
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin-bottom: 15px; color: #721c24;'>
                        ❌ بيانات تسجيل الدخول غير صحيحة
                      </div>";
            }
        }
        ?>

        <form method="POST">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit" class="btn">دخول النظام</button>
        </form>
        
        <div style="margin-top: 20px; text-align: center; color: #666;">
            <p><strong>بيانات تسجيل الدخول:</strong></p>
            <p>المستخدم: admin | كلمة المرور: admin123</p>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">⚠️ ملاحظة مهمة</h4>
            <p style="margin: 0; color: #856404; font-size: 14px;">
                لاستخدام النظام الكامل مع قاعدة البيانات، يرجى حل مشكلة كلمة مرور MySQL أولاً.
            </p>
        </div>
    </div>
</body>
</html>
