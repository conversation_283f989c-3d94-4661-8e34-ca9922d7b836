<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين MySQL - نقطة البيع</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; }
        .warning { background: #fff3cd; border-left: 5px solid #ffc107; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; }
        .success { background: #d4edda; border-left: 5px solid #28a745; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 3px solid #007cba; }
        .code { background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; direction: ltr; text-align: left; }
        h1, h2, h3 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card error">
            <h1>🚨 مشكلة في MySQL</h1>
            <p>يبدو أن MySQL يحتاج إعادة تعيين. إليك الحلول المجربة:</p>
        </div>

        <div class="card">
            <h2>🔧 الحل الأول: إعادة تعيين MySQL عبر XAMPP</h2>
            <div class="step">
                <h3>الخطوات:</h3>
                <ol>
                    <li><strong>أغلق XAMPP تماماً</strong></li>
                    <li><strong>افتح XAMPP كمدير</strong> (Run as Administrator)</li>
                    <li><strong>اضغط Config بجانب MySQL</strong> واختر <code>my.ini</code></li>
                    <li><strong>ابحث عن [mysqld]</strong> وأضف تحته:</li>
                </ol>
                <div class="code">skip-grant-tables</div>
                <ol start="5">
                    <li><strong>احفظ الملف</strong></li>
                    <li><strong>شغل MySQL</strong> من XAMPP</li>
                    <li><strong>ارجع لهذه الصفحة</strong> واضغط "اختبار الاتصال"</li>
                </ol>
            </div>
        </div>

        <div class="card">
            <h2>🔧 الحل الثاني: إعادة تثبيت MySQL</h2>
            <div class="step">
                <h3>إذا لم يعمل الحل الأول:</h3>
                <ol>
                    <li>أوقف MySQL من XAMPP</li>
                    <li>احذف مجلد <code>C:\xampp\mysql\data</code></li>
                    <li>انسخ مجلد <code>C:\xampp\mysql\backup</code> إلى <code>C:\xampp\mysql\data</code></li>
                    <li>أعد تشغيل MySQL</li>
                </ol>
            </div>
        </div>

        <div class="card success">
            <h2>🚀 حل سريع: استخدام النسخة التجريبية</h2>
            <p>بينما تحل مشكلة MySQL، يمكنك استخدام النسخة التجريبية من النظام:</p>
            <a href="demo_system.php" class="btn btn-success">تشغيل النسخة التجريبية</a>
        </div>

        <div class="card info">
            <h2>🧪 اختبار الاتصال</h2>
            <p>بعد تطبيق أي من الحلول أعلاه:</p>
            <a href="test_connection.php" class="btn">اختبار الاتصال</a>
        </div>

        <div class="card warning">
            <h2>📞 مساعدة إضافية</h2>
            <p>إذا استمرت المشكلة، جرب:</p>
            <ul>
                <li><strong>إعادة تشغيل الكمبيوتر</strong> وتشغيل XAMPP كمدير</li>
                <li><strong>تحديث XAMPP</strong> إلى أحدث إصدار</li>
                <li><strong>فحص منافذ النظام</strong> للتأكد من عدم تعارض البرامج</li>
            </ul>
        </div>
    </div>
</body>
</html>
