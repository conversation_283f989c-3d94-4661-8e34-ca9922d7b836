<?php
// اختبار الاتصال بقاعدة البيانات
$host = "localhost";
$user = "root";
$pass = "";
$db = "pos_system";

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    $conn = new mysqli($host, $user, $pass, $db);
    
    if ($conn->connect_error) {
        echo "<p style='color: red;'>فشل الاتصال: " . $conn->connect_error . "</p>";
        
        // محاولة إنشاء قاعدة البيانات
        echo "<p>محاولة إنشاء قاعدة البيانات...</p>";
        $conn_no_db = new mysqli($host, $user, $pass);
        
        if ($conn_no_db->connect_error) {
            echo "<p style='color: red;'>فشل الاتصال بـ MySQL: " . $conn_no_db->connect_error . "</p>";
        } else {
            $sql = "CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if ($conn_no_db->query($sql) === TRUE) {
                echo "<p style='color: green;'>تم إنشاء قاعدة البيانات بنجاح!</p>";
                echo "<p><a href='setup_db.php'>اضغط هنا لإعداد الجداول</a></p>";
            } else {
                echo "<p style='color: red;'>خطأ في إنشاء قاعدة البيانات: " . $conn_no_db->error . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>تم الاتصال بقاعدة البيانات بنجاح!</p>";
        
        // التحقق من وجود الجداول
        $result = $conn->query("SHOW TABLES");
        if ($result->num_rows > 0) {
            echo "<p style='color: green;'>الجداول موجودة:</p><ul>";
            while($row = $result->fetch_array()) {
                echo "<li>" . $row[0] . "</li>";
            }
            echo "</ul>";
            echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام</a></p>";
        } else {
            echo "<p style='color: orange;'>قاعدة البيانات فارغة. تحتاج لإعداد الجداول.</p>";
            echo "<p><a href='setup_db.php'>اضغط هنا لإعداد الجداول</a></p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
