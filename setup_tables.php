<?php
// إعداد الجداول إذا لم تكن موجودة
include 'config/db.php';

echo "<h2>إعداد جداول قاعدة البيانات</h2>";

try {
    // التحقق من وجود الجداول
    $result = $conn->query("SHOW TABLES");
    $existing_tables = [];
    
    if ($result) {
        while ($row = $result->fetch_array()) {
            $existing_tables[] = $row[0];
        }
    }
    
    if (count($existing_tables) > 0) {
        echo "<p style='color: green;'>✓ الجداول موجودة مسبقاً: " . implode(', ', $existing_tables) . "</p>";
        echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام</a></p>";
    } else {
        echo "<p>إنشاء الجداول...</p>";
        
        // قراءة ملف SQL
        if (file_exists('sql/schema.sql')) {
            $sql_content = file_get_contents('sql/schema.sql');
            $queries = explode(';', $sql_content);
            
            $success_count = 0;
            foreach ($queries as $query) {
                $query = trim($query);
                if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^CREATE DATABASE/', $query)) {
                    if ($conn->query($query)) {
                        $success_count++;
                        echo "<p style='color: green;'>✓ " . substr($query, 0, 50) . "...</p>";
                    } else {
                        echo "<p style='color: red;'>✗ خطأ في: " . substr($query, 0, 50) . "...</p>";
                    }
                }
            }
            
            if ($success_count > 0) {
                echo "<h3 style='color: green;'>تم إعداد الجداول بنجاح!</h3>";
                echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
                echo "<ul><li>اسم المستخدم: admin</li><li>كلمة المرور: admin123</li></ul>";
                echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام</a></p>";
            }
        } else {
            echo "<p style='color: red;'>ملف sql/schema.sql غير موجود</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
