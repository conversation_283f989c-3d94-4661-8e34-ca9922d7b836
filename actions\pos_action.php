
<?php
include("../config/db.php");
if (session_status()===PHP_SESSION_NONE) session_start();
header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? '';

if ($action === 'find') {
    $code = $conn->real_escape_string($_GET['code'] ?? '');
    if ($code === '') { echo json_encode(null); exit; }
    // البحث بالباركود أو الاسم أو id
    $sql = "SELECT id, name, price FROM products WHERE barcode='$code' OR name LIKE '%$code%' OR id='$code' LIMIT 1";
    $res = $conn->query($sql);
    echo json_encode($res && $res->num_rows ? $res->fetch_assoc() : null);
    exit;
}

if ($action === 'sale') {
    $data = json_decode(file_get_contents('php://input'), true);
    $customer_id = intval($data['customer_id'] ?? 1);
    $payment_type = ($data['payment_type'] ?? 'cash') === 'credit' ? 'credit' : 'cash';
    $items = $data['items'] ?? [];
    if (!is_array($items) || count($items)==0) { echo json_encode(['success'=>false,'message'=>'لا توجد أصناف']); exit; }

    // حساب الإجمالي
    $total = 0;
    foreach($items as $it){
        $qty = intval($it['qty'] ?? 1);
        $price = floatval($it['price'] ?? 0);
        $total += $qty * $price;
    }

    // إنشاء الفاتورة
    $user_id = $_SESSION['user_id'] ?? 1;
    $conn->begin_transaction();
    try{
        $conn->query("INSERT INTO sales (customer_id, user_id, total, payment_type) VALUES ($customer_id, $user_id, $total, '$payment_type')");
        $sale_id = $conn->insert_id;

        foreach($items as $it){
            $pid = intval($it['id']);
            $qty = intval($it['qty']);
            $price = floatval($it['price']);
            $subtotal = $qty * $price;
            $conn->query("INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) VALUES ($sale_id, $pid, $qty, $price, $subtotal)");
            // خصم من المخزون
            $conn->query("UPDATE products SET stock = GREATEST(0, stock - $qty) WHERE id=$pid");
        }

        if($payment_type === 'credit'){
            // زيادة رصيد العميل (دين)
            $conn->query("UPDATE customers SET balance = balance + $total WHERE id=$customer_id");
        }

        $conn->commit();
        echo json_encode(['success'=>true,'sale_id'=>$sale_id]);
    }catch(Exception $e){
        $conn->rollback();
        echo json_encode(['success'=>false,'message'=>'فشل إنشاء الفاتورة']);
    }
    exit;
}

echo json_encode(['success'=>false,'message'=>'إجراء غير معروف']);
