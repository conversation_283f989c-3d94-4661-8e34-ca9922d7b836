
<?php include("../includes/header.php"); include("../config/db.php"); ?>
<div class="card p-3 shadow-sm">
  <div class="d-flex flex-wrap gap-2 mb-3">
    <input id="barcode" class="form-control" style="max-width:200px" placeholder="باركود / كود">
    <input id="search" class="form-control" style="max-width:260px" placeholder="بحث بالاسم">
    <input id="qty" type="number" class="form-control" style="max-width:120px" value="1" min="1">
    <button class="btn btn-primary" onclick="addByBarcode()">إضافة</button>
  </div>

  <div class="table-responsive">
    <table class="table table-bordered align-middle" id="cartTable">
      <thead class="table-light">
        <tr>
          <th>المنتج</th><th>السعر</th><th>الكمية</th><th>المجموع</th><th>حذف</th>
        </tr>
      </thead>
      <tbody></tbody>
    </table>
  </div>

  <div class="row mt-3">
    <div class="col-md-4">
      <label class="form-label">العميل</label>
      <select id="customer" class="form-select">
        <?php
          $cs = $conn->query("SELECT id, name FROM customers ORDER BY id ASC");
          while($c = $cs->fetch_assoc()) {
            echo "<option value='{$c['id']}'>{$c['name']}</option>";
          }
        ?>
      </select>
      <label class="form-label mt-2">ملاحظة (للتعليق)</label>
      <input id="hold_note" class="form-control" placeholder="مثال: زبون عند الصراف">
    </div>
    <div class="col-md-4">
      <label class="form-label">طريقة الدفع</label>
      <select id="payment_type" class="form-select">
        <option value="cash">نقدي</option>
        <option value="credit">آجل</option>
      </select>
    </div>
    <div class="col-md-4">
      <div class="p-3 bg-light rounded">
        <div class="d-flex justify-content-between"><b>الإجمالي:</b> <b id="total">0.00</b></div>
      </div>
    </div>
  </div>

  <div class="d-flex gap-2 mt-3">
    <button class="btn btn-success" onclick="submitSale()">إنهاء واصدار فاتورة</button>
    <button class="btn btn-warning" onclick="holdInvoice()">تعليق الفاتورة</button>
    <a class="btn btn-info" href="held_invoices.php">استعادة فاتورة معلقة</a>
    <button class="btn btn-outline-danger" onclick="clearCart()">تفريغ</button>
  </div>
</div>

<script>
let cart = [];

function renderCart(){
  const tbody = document.querySelector("#cartTable tbody");
  tbody.innerHTML = "";
  let total = 0;
  cart.forEach((item, idx)=>{
    const tr = document.createElement("tr");
    const subtotal = (item.price * item.qty);
    total += subtotal;
    tr.innerHTML = `
      <td>${item.name}</td>
      <td>${item.price.toFixed(2)}</td>
      <td><input type='number' min='1' value='${item.qty}' class='form-control' style='max-width:100px' onchange='updateQty(${idx}, this.value)'></td>
      <td>${subtotal.toFixed(2)}</td>
      <td><button class='btn btn-sm btn-danger' onclick='removeItem(${idx})'>✕</button></td>
    `;
    tbody.appendChild(tr);
  });
  document.getElementById("total").innerText = total.toFixed(2);
}

function updateQty(i, v){ cart[i].qty = parseInt(v||1); renderCart(); }
function removeItem(i){ cart.splice(i,1); renderCart(); }
function clearCart(){ cart = []; renderCart(); }

async function addByBarcode(){
  const code = document.getElementById('barcode').value.trim() || document.getElementById('search').value.trim();
  const qty = parseInt(document.getElementById('qty').value || '1');
  if(!code) return;
  const res = await fetch('../actions/pos_action.php?action=find&code='+encodeURIComponent(code));
  const p = await res.json();
  if(p && p.id){
    const exist = cart.find(x=>x.id==p.id);
    if(exist){ exist.qty += qty; } else { cart.push({id:p.id, name:p.name, price:parseFloat(p.price), qty:qty}); }
    document.getElementById('barcode').value=''; document.getElementById('search').value='';
    renderCart();
  }else{
    alert('المنتج غير موجود');
  }
}

async function submitSale(){
  if(cart.length===0) return alert('لا توجد أصناف');
  const payload = {
    customer_id: document.getElementById('customer').value,
    payment_type: document.getElementById('payment_type').value,
    items: cart
  };
  const res = await fetch('../actions/pos_action.php?action=sale', {
    method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload)
  });
  const out = await res.json();
  if(out.success){
    alert('تم إصدار الفاتورة رقم: '+out.sale_id);
    clearCart();
  }else{
    alert('خطأ: '+out.message);
  }
}

async function holdInvoice(){
  if(cart.length===0) return alert('لا توجد أصناف');
  const payload = {
    customer_id: document.getElementById('customer').value,
    note: document.getElementById('hold_note').value || null,
    items: cart
  };
  const res = await fetch('../actions/invoice_action.php?action=hold', {
    method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload)
  });
  const out = await res.json();
  if(out.success){
    alert('تم تعليق الفاتورة رقم: '+out.held_id);
    clearCart();
  }else{
    alert('خطأ: '+out.message);
  }
}
</script>
<?php include("../includes/footer.php"); ?>
