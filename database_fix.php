<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح قاعدة البيانات - نقطة البيع</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; border-left: 5px solid #28a745; }
        .error { background: #f8d7da; border-left: 5px solid #dc3545; }
        .warning { background: #fff3cd; border-left: 5px solid #ffc107; }
        .info { background: #d1ecf1; border-left: 5px solid #17a2b8; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #212529; }
        h1, h2, h3 { color: #333; }
        .step { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1>🔧 إصلاح الاتصال بقاعدة البيانات</h1>
            <p>هذه الأداة ستساعدك في حل مشكلة الاتصال بـ MySQL خطوة بخطوة.</p>
        </div>

        <?php
        // الخطوة 1: فحص حالة MySQL
        echo "<div class='card'>";
        echo "<h2>📊 الخطوة 1: فحص حالة MySQL</h2>";
        
        $mysql_running = false;
        $connection = @fsockopen('localhost', 3306, $errno, $errstr, 1);
        if ($connection) {
            echo "<div class='success'><strong>✓ MySQL يعمل</strong> - الخدمة متاحة على المنفذ 3306</div>";
            fclose($connection);
            $mysql_running = true;
        } else {
            echo "<div class='error'><strong>✗ MySQL لا يعمل</strong> - يرجى تشغيل MySQL من XAMPP Control Panel</div>";
        }
        echo "</div>";

        // الخطوة 2: اختبار كلمات المرور
        if ($mysql_running) {
            echo "<div class='card'>";
            echo "<h2>🔑 الخطوة 2: اختبار كلمات المرور</h2>";
            
            $passwords = [
                "" => "بدون كلمة مرور",
                "root" => "root", 
                "123456" => "123456",
                "password" => "password",
                "admin" => "admin",
                "mysql" => "mysql",
                "xampp" => "xampp"
            ];
            
            $working_password = null;
            $working_password_name = "";
            
            foreach ($passwords as $pass => $name) {
                echo "<div class='step'>";
                echo "<strong>اختبار كلمة المرور:</strong> $name ... ";
                
                try {
                    $conn = @new mysqli('localhost', 'root', $pass);
                    
                    if (!$conn->connect_error) {
                        echo "<span style='color: green;'><strong>✓ نجحت!</strong></span>";
                        $working_password = $pass;
                        $working_password_name = $name;
                        $conn->close();
                        break;
                    } else {
                        echo "<span style='color: red;'>✗ فشلت</span>";
                    }
                } catch (Exception $e) {
                    echo "<span style='color: red;'>✗ خطأ</span>";
                }
                echo "</div>";
            }
            
            if ($working_password !== null) {
                echo "<div class='success'>";
                echo "<h3>🎉 تم العثور على كلمة المرور الصحيحة!</h3>";
                echo "<p><strong>كلمة المرور:</strong> $working_password_name</p>";
                echo "</div>";
                
                // الخطوة 3: تحديث ملف الإعدادات
                echo "<h2>⚙️ الخطوة 3: تحديث ملف الإعدادات</h2>";
                
                $config_content = "<?php\n";
                $config_content .= "\$host = \"localhost\";\n";
                $config_content .= "\$user = \"root\";\n";
                $config_content .= "\$pass = \"$working_password\";\n";
                $config_content .= "\$db   = \"pos_system\";\n\n";
                $config_content .= "\$conn = new mysqli(\$host, \$user, \$pass, \$db);\n\n";
                $config_content .= "if (\$conn->connect_error) {\n";
                $config_content .= "    die(\"فشل الاتصال بقاعدة البيانات: \" . \$conn->connect_error);\n";
                $config_content .= "}\n";
                $config_content .= "?>\n";
                
                if (file_put_contents('config/db.php', $config_content)) {
                    echo "<div class='success'>✓ تم تحديث ملف config/db.php بنجاح!</div>";
                } else {
                    echo "<div class='error'>✗ فشل في تحديث ملف الإعدادات</div>";
                }
                
                // الخطوة 4: إنشاء قاعدة البيانات
                echo "<h2>🗄️ الخطوة 4: إعداد قاعدة البيانات</h2>";
                
                try {
                    $conn = new mysqli('localhost', 'root', $working_password);
                    
                    // إنشاء قاعدة البيانات
                    $sql = "CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                    if ($conn->query($sql) === TRUE) {
                        echo "<div class='success'>✓ تم إنشاء قاعدة البيانات pos_system</div>";
                    }
                    
                    // الاتصال بقاعدة البيانات
                    $conn->select_db('pos_system');
                    
                    // تنفيذ ملف SQL
                    if (file_exists('sql/schema.sql')) {
                        $sql_content = file_get_contents('sql/schema.sql');
                        
                        // تقسيم الاستعلامات
                        $queries = explode(';', $sql_content);
                        $success_count = 0;
                        $total_queries = 0;
                        
                        foreach ($queries as $query) {
                            $query = trim($query);
                            if (!empty($query) && !preg_match('/^--/', $query) && !preg_match('/^\/\*/', $query)) {
                                $total_queries++;
                                if ($conn->query($query) === TRUE) {
                                    $success_count++;
                                }
                            }
                        }
                        
                        if ($success_count > 0) {
                            echo "<div class='success'>";
                            echo "<h3>✓ تم إعداد الجداول بنجاح!</h3>";
                            echo "<p>تم تنفيذ $success_count من $total_queries استعلام بنجاح</p>";
                            echo "</div>";
                            
                            // اختبار الاتصال النهائي
                            echo "<h2>🧪 الخطوة 5: اختبار الاتصال النهائي</h2>";
                            
                            $final_conn = new mysqli('localhost', 'root', $working_password, 'pos_system');
                            if (!$final_conn->connect_error) {
                                echo "<div class='success'>";
                                echo "<h3>🎉 تم إعداد النظام بنجاح!</h3>";
                                echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
                                echo "<ul>";
                                echo "<li>اسم المستخدم: <strong>admin</strong></li>";
                                echo "<li>كلمة المرور: <strong>admin123</strong></li>";
                                echo "</ul>";
                                echo "<a href='views/login.php' class='btn btn-success'>🚀 دخول النظام الآن</a>";
                                echo "<a href='views/pos.php' class='btn'>📱 نقطة البيع مباشرة</a>";
                                echo "</div>";
                            } else {
                                echo "<div class='error'>✗ فشل في الاتصال النهائي: " . $final_conn->connect_error . "</div>";
                            }
                        } else {
                            echo "<div class='error'>✗ فشل في إعداد الجداول</div>";
                        }
                    } else {
                        echo "<div class='error'>✗ ملف sql/schema.sql غير موجود</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='error'>خطأ في إعداد قاعدة البيانات: " . $e->getMessage() . "</div>";
                }
                
            } else {
                echo "<div class='error'>";
                echo "<h3>❌ لم يتم العثور على كلمة مرور صحيحة</h3>";
                echo "<p>جرب الحلول التالية:</p>";
                echo "</div>";
                
                echo "<div class='card warning'>";
                echo "<h3>🔧 حلول بديلة:</h3>";
                echo "<div class='step'>";
                echo "<h4>1. إعادة تعيين كلمة مرور MySQL:</h4>";
                echo "<ol>";
                echo "<li>افتح XAMPP Control Panel كمدير</li>";
                echo "<li>اضغط 'Stop' بجانب MySQL</li>";
                echo "<li>اضغط 'Config' بجانب MySQL واختر 'my.ini'</li>";
                echo "<li>أضف هذا السطر تحت [mysqld]:</li>";
                echo "<div class='code'>skip-grant-tables</div>";
                echo "<li>احفظ الملف وأعد تشغيل MySQL</li>";
                echo "<li>ارجع لهذه الصفحة</li>";
                echo "</ol>";
                echo "</div>";
                
                echo "<div class='step'>";
                echo "<h4>2. استخدام phpMyAdmin:</h4>";
                echo "<p><a href='http://localhost/phpmyadmin' target='_blank' class='btn'>فتح phpMyAdmin</a></p>";
                echo "<p>إذا دخلت بنجاح، أنشئ قاعدة بيانات باسم 'pos_system' واستورد ملف sql/schema.sql</p>";
                echo "</div>";
                echo "</div>";
            }
            echo "</div>";
        }
        ?>
        
        <div class="card info">
            <h3>🔄 إعادة المحاولة</h3>
            <p>بعد تطبيق أي من الحلول أعلاه:</p>
            <a href="database_fix.php" class="btn">إعادة فحص الاتصال</a>
        </div>
    </div>
</body>
</html>
