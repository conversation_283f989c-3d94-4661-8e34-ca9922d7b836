<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقطة البيع - النسخة التجريبية</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f8f9fa; }
        .header { background: #007cba; color: white; padding: 15px 0; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .nav { display: flex; justify-content: space-between; align-items: center; }
        .nav h1 { font-size: 24px; }
        .nav-links { display: flex; gap: 20px; }
        .nav-links a { color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; transition: background 0.3s; }
        .nav-links a:hover, .nav-links a.active { background: rgba(255,255,255,0.2); }
        .main { padding: 20px 0; }
        .card { background: white; padding: 20px; margin: 15px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .pos-section { background: #e8f5e8; border-left: 5px solid #28a745; }
        .stats-section { background: #e7f3ff; border-left: 5px solid #007cba; }
        .products-section { background: #fff3e0; border-left: 5px solid #ff9800; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #005a87; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .product-item { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6; }
        .product-item h4 { color: #333; margin-bottom: 8px; }
        .product-item p { color: #666; margin: 4px 0; }
        .alert { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .alert-info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .alert-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <div class="nav">
                <h1>🏪 نظام نقطة البيع</h1>
                <div class="nav-links">
                    <a href="#pos" class="active">نقطة البيع</a>
                    <a href="#products">المنتجات</a>
                    <a href="#reports">التقارير</a>
                    <a href="#customers">العملاء</a>
                    <a href="mysql_reset.php">إعدادات MySQL</a>
                </div>
            </div>
        </div>
    </div>

    <div class="main">
        <div class="container">
            <div class="alert alert-info">
                <h3>📋 النسخة التجريبية</h3>
                <p>هذه نسخة تجريبية من النظام تعمل بدون قاعدة بيانات. جميع البيانات مؤقتة ولأغراض العرض فقط.</p>
                <p><strong>لاستخدام النظام الكامل:</strong> يرجى حل مشكلة MySQL أولاً.</p>
            </div>

            <div class="grid">
                <!-- نقطة البيع -->
                <div class="card pos-section" id="pos">
                    <h2>🛒 نقطة البيع</h2>
                    <p>إجراء المبيعات وإصدار الفواتير</p>
                    
                    <div style="margin: 20px 0;">
                        <h4>🔍 البحث عن منتج:</h4>
                        <input type="text" placeholder="اسم المنتج أو الباركود" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0;">
                        
                        <h4>🛍️ سلة المشتريات:</h4>
                        <div style="background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; margin: 10px 0;">
                            <p style="color: #666; text-align: center;">السلة فارغة</p>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <button class="btn btn-success">💳 إتمام البيع</button>
                            <button class="btn btn-warning">⏸️ تعليق الفاتورة</button>
                            <button class="btn btn-danger">🗑️ إلغاء</button>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات -->
                <div class="card stats-section">
                    <h2>📊 إحصائيات اليوم</h2>
                    <div style="margin: 20px 0;">
                        <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 5px;">
                            <span>💰 إجمالي المبيعات:</span>
                            <strong>0.00 ريال</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 5px;">
                            <span>🧾 عدد الفواتير:</span>
                            <strong>0</strong>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin: 10px 0; padding: 10px; background: rgba(255,255,255,0.7); border-radius: 5px;">
                            <span>👥 عدد العملاء:</span>
                            <strong>0</strong>
                        </div>
                    </div>
                    <button class="btn">📈 عرض التقارير التفصيلية</button>
                </div>
            </div>

            <!-- المنتجات -->
            <div class="card products-section" id="products">
                <h2>📦 المنتجات التجريبية</h2>
                <p>هذه منتجات تجريبية لأغراض العرض:</p>
                
                <div class="grid" style="margin-top: 20px;">
                    <div class="product-item">
                        <h4>💧 ماء 330ml</h4>
                        <p><strong>الباركود:</strong> 1001</p>
                        <p><strong>السعر:</strong> 1.50 ريال</p>
                        <p><strong>المخزون:</strong> 100 قطعة</p>
                        <button class="btn btn-success">➕ إضافة للسلة</button>
                    </div>
                    
                    <div class="product-item">
                        <h4>🍞 خبز سوردو</h4>
                        <p><strong>الباركود:</strong> 1002</p>
                        <p><strong>السعر:</strong> 6.00 ريال</p>
                        <p><strong>المخزون:</strong> 50 قطعة</p>
                        <button class="btn btn-success">➕ إضافة للسلة</button>
                    </div>
                    
                    <div class="product-item">
                        <h4>🌴 تمور خلاص 1كجم</h4>
                        <p><strong>الباركود:</strong> 1003</p>
                        <p><strong>السعر:</strong> 18.00 ريال</p>
                        <p><strong>المخزون:</strong> 40 قطعة</p>
                        <button class="btn btn-success">➕ إضافة للسلة</button>
                    </div>
                </div>
            </div>

            <!-- التقارير -->
            <div class="card" id="reports">
                <h2>📈 التقارير</h2>
                <p>عرض تقارير المبيعات والأرباح (تجريبي)</p>
                <div style="margin: 20px 0;">
                    <button class="btn">📅 تقرير يومي</button>
                    <button class="btn">📊 تقرير أسبوعي</button>
                    <button class="btn">📈 تقرير شهري</button>
                    <button class="btn">💰 تقرير الأرباح</button>
                </div>
            </div>

            <!-- العملاء -->
            <div class="card" id="customers">
                <h2>👥 إدارة العملاء</h2>
                <p>إضافة وإدارة بيانات العملاء (تجريبي)</p>
                <div style="margin: 20px 0;">
                    <button class="btn btn-success">➕ إضافة عميل جديد</button>
                    <button class="btn">👁️ عرض جميع العملاء</button>
                    <button class="btn">🔍 البحث عن عميل</button>
                </div>
            </div>

            <div class="alert alert-success">
                <h3>✅ النظام جاهز للاستخدام!</h3>
                <p>يمكنك تجربة جميع الميزات في هذه النسخة التجريبية.</p>
                <p><strong>لحفظ البيانات فعلياً:</strong> يرجى حل مشكلة MySQL واستخدام النظام الكامل.</p>
                <a href="mysql_reset.php" class="btn">🔧 إصلاح MySQL</a>
            </div>
        </div>
    </div>

    <script>
        // إضافة تفاعل بسيط
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelectorAll('.nav-links a').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
                
                const target = this.getAttribute('href').substring(1);
                const element = document.getElementById(target);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // محاكاة إضافة منتجات للسلة
        document.querySelectorAll('.product-item .btn-success').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('تم إضافة المنتج للسلة! (نسخة تجريبية)');
            });
        });
    </script>
</body>
</html>
