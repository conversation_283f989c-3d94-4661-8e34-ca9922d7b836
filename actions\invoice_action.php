
<?php
include("../config/db.php");
if (session_status()===PHP_SESSION_NONE) session_start();

$action = $_GET['action'] ?? '';
if ($action === 'hold') {
    header('Content-Type: application/json; charset=utf-8');
    $data = json_decode(file_get_contents('php://input'), true);
    $customer_id = isset($data['customer_id']) ? intval($data['customer_id']) : 'NULL';
    $note = $conn->real_escape_string($data['note'] ?? '');
    $items = $data['items'] ?? [];
    if (!is_array($items) || count($items)===0) { echo json_encode(['success'=>false,'message'=>'لا توجد أصناف']); exit; }

    $total = 0;
    foreach($items as $it){ $total += (float)$it['price'] * (int)$it['qty']; }
    $user_id = $_SESSION['user_id'] ?? 1;
    $items_json = $conn->real_escape_string(json_encode($items, JSON_UNESCAPED_UNICODE));
    $note_sql = $note ? "'$note'" : "NULL";
    $cust_sql = $customer_id ? $customer_id : "NULL";
    $sql = "INSERT INTO held_invoices (customer_id, user_id, note, items, total) VALUES ($cust_sql, $user_id, $note_sql, '$items_json', $total)";
    if($conn->query($sql)){
        echo json_encode(['success'=>true,'held_id'=>$conn->insert_id]);
    }else{
        echo json_encode(['success'=>false,'message'=>'تعذر التعليق']);
    }
    exit;
}

if ($action === 'resume') {
    // استعادة: يعرض JSON أو يقوم بالبيع مباشرة؟
    // هنا سنحوّل الفاتورة المعلقة إلى شاشة POS عبر إنشاء بيع مباشر (اختياري) أو يمكن فقط إرجاع JSON.
    $id = intval($_GET['id'] ?? 0);
    if($id<=0){ die("معرّف غير صحيح"); }
    $q = $conn->query("SELECT * FROM held_invoices WHERE id=$id");
    if(!$q || !$q->num_rows){ die("غير موجود"); }
    $row = $q->fetch_assoc();
    // سنعرض صفحة بسيطة تعرض العناصر مع زر لتحويلها إلى فاتورة
    header("Content-Type: text/html; charset=utf-8");
    $items = json_decode($row['items'], true);
    ?>
    <!DOCTYPE html><html lang="ar" dir="rtl"><head>
    <meta charset="UTF-8"><title>استعادة فاتورة معلقة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    </head><body class="p-3">
      <div class="container">
        <h4>فاتورة معلقة #<?= $row['id'] ?></h4>
        <p>العميل: <?= $row['customer_id'] ?: 'بدون' ?> | الملاحظة: <?= htmlspecialchars($row['note'] ?? '') ?> | الإجمالي: <?= number_format($row['total'],2) ?></p>
        <table class="table table-bordered">
          <thead><tr><th>المنتج</th><th>السعر</th><th>الكمية</th><th>المجموع</th></tr></thead>
          <tbody>
          <?php foreach($items as $it): ?>
            <tr><td><?= htmlspecialchars($it['name']) ?></td><td><?= number_format($it['price'],2) ?></td><td><?= (int)$it['qty'] ?></td><td><?= number_format($it['price']*$it['qty'],2) ?></td></tr>
          <?php endforeach; ?>
          </tbody>
        </table>
        <form method="post" action="invoice_action.php?action=convert_sale">
          <input type="hidden" name="id" value="<?= $row['id'] ?>">
          <button class="btn btn-success">تحويل إلى فاتورة</button>
          <a class="btn btn-secondary" href="../views/held_invoices.php">رجوع</a>
        </form>
      </div>
    </body></html>
    <?php
    exit;
}

if ($action === 'convert_sale') {
    $id = intval($_POST['id'] ?? 0);
    if($id<=0){ die("خطأ في المعرّف"); }
    $q = $conn->query("SELECT * FROM held_invoices WHERE id=$id");
    if(!$q || !$q->num_rows){ die("الفاتورة غير موجودة"); }
    $row = $q->fetch_assoc();
    $items = json_decode($row['items'], true);
    $total = (float)$row['total'];
    $customer_id = $row['customer_id'] ? intval($row['customer_id']) : 1; // افتراضي
    $user_id = $row['user_id'];

    $conn->begin_transaction();
    try{
        $conn->query("INSERT INTO sales (customer_id, user_id, total, payment_type) VALUES ($customer_id, $user_id, $total, 'cash')");
        $sale_id = $conn->insert_id;
        foreach($items as $it){
            $pid = intval($it['id']);
            $qty = intval($it['qty']);
            $price = floatval($it['price']);
            $subtotal = $qty * $price;
            $conn->query("INSERT INTO sale_items (sale_id, product_id, quantity, price, subtotal) VALUES ($sale_id, $pid, $qty, $price, $subtotal)");
            $conn->query("UPDATE products SET stock = GREATEST(0, stock - $qty) WHERE id=$pid");
        }
        // حذف الفاتورة المعلقة
        $conn->query("DELETE FROM held_invoices WHERE id=$id");
        $conn->commit();
        header("Location: ../views/held_invoices.php");
    }catch(Exception $e){
        $conn->rollback();
        die("فشل التحويل");
    }
    exit;
}

if ($action === 'delete') {
    $id = intval($_GET['id'] ?? 0);
    if($id>0){
        $conn->query("DELETE FROM held_invoices WHERE id=$id");
    }
    header("Location: ../views/held_invoices.php");
    exit;
}

echo "إجراء غير معروف";
