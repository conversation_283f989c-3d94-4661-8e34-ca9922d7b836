<?php
// اختبار الاتصال بقاعدة البيانات مع كلمات مرور مختلفة
$host = "localhost";
$user = "root";
$db = "pos_system";

// كلمات المرور المحتملة
$passwords = ["", "root", "123456", "password", "admin"];

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

$connected = false;
$working_password = "";

foreach ($passwords as $pass) {
    try {
        echo "<p>جاري اختبار كلمة المرور: " . ($pass == "" ? "(فارغة)" : $pass) . "</p>";

        $conn_test = new mysqli($host, $user, $pass);

        if (!$conn_test->connect_error) {
            echo "<p style='color: green;'>✓ نجح الاتصال بكلمة المرور: " . ($pass == "" ? "(فارغة)" : $pass) . "</p>";
            $connected = true;
            $working_password = $pass;
            break;
        } else {
            echo "<p style='color: red;'>✗ فشل بكلمة المرور: " . ($pass == "" ? "(فارغة)" : $pass) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ خطأ مع كلمة المرور " . ($pass == "" ? "(فارغة)" : $pass) . ": " . $e->getMessage() . "</p>";
    }
}

if ($connected) {
    echo "<h3 style='color: green;'>تم العثور على كلمة المرور الصحيحة!</h3>";

    // تحديث ملف الإعدادات
    $config_content = "<?php\n";
    $config_content .= "\$host = \"localhost\";\n";
    $config_content .= "\$user = \"root\";\n";
    $config_content .= "\$pass = \"$working_password\";\n";
    $config_content .= "\$db   = \"pos_system\";\n\n";
    $config_content .= "\$conn = new mysqli(\$host, \$user, \$pass, \$db);\n\n";
    $config_content .= "if (\$conn->connect_error) {\n";
    $config_content .= "    die(\"فشل الاتصال بقاعدة البيانات: \" . \$conn->connect_error);\n";
    $config_content .= "}\n";
    $config_content .= "?>\n";

    file_put_contents('config/db.php', $config_content);
    echo "<p style='color: green;'>تم تحديث ملف الإعدادات تلقائياً!</p>";

    // التحقق من قاعدة البيانات
    try {
        $conn = new mysqli($host, $user, $working_password, $db);

        if ($conn->connect_error) {
            echo "<p style='color: orange;'>قاعدة البيانات غير موجودة. سيتم إنشاؤها...</p>";
            $conn_no_db = new mysqli($host, $user, $working_password);
            $sql = "CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            if ($conn_no_db->query($sql) === TRUE) {
                echo "<p style='color: green;'>تم إنشاء قاعدة البيانات بنجاح!</p>";
            }
            echo "<p><a href='setup_db.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد الجداول</a></p>";
        } else {
            echo "<p style='color: green;'>قاعدة البيانات موجودة!</p>";

            // التحقق من وجود الجداول
            $result = $conn->query("SHOW TABLES");
            if ($result && $result->num_rows > 0) {
                echo "<p style='color: green;'>الجداول موجودة:</p><ul>";
                while($row = $result->fetch_array()) {
                    echo "<li>" . $row[0] . "</li>";
                }
                echo "</ul>";
                echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام</a></p>";
            } else {
                echo "<p style='color: orange;'>قاعدة البيانات فارغة. تحتاج لإعداد الجداول.</p>";
                echo "<p><a href='setup_db.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إعداد الجداول</a></p>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في التحقق من قاعدة البيانات: " . $e->getMessage() . "</p>";
    }

} else {
    echo "<h3 style='color: red;'>لم يتم العثور على كلمة مرور صحيحة!</h3>";
    echo "<p>يرجى التحقق من إعدادات MySQL في XAMPP أو تعيين كلمة مرور للمستخدم root.</p>";
    echo "<p>يمكنك تعيين كلمة مرور من خلال phpMyAdmin أو سطر الأوامر.</p>";
}
?>
