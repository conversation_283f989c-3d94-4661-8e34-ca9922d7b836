
<?php include("../includes/header.php"); include("../config/db.php"); ?>
<div class="row g-3">
  <div class="col-md-3">
    <div class="card p-3 shadow-sm">
      <h6 class="text-muted">إجمالي المبيعات (اليوم)</h6>
      <?php
        $today = date('Y-m-d');
        $q = $conn->query("SELECT IFNULL(SUM(total),0) AS t FROM sales WHERE DATE(created_at)='$today'");
        $t = $q ? $q->fetch_assoc()['t'] : 0;
      ?>
      <h3><?= number_format($t,2) ?> ر.س</h3>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card p-3 shadow-sm">
      <h6 class="text-muted">عدد الفواتير</h6>
      <?php
        $q = $conn->query("SELECT COUNT(*) AS c FROM sales");
        $c = $q ? $q->fetch_assoc()['c'] : 0;
      ?>
      <h3><?= $c ?></h3>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card p-3 shadow-sm">
      <h6 class="text-muted">فواتير معلقة</h6>
      <?php
        $q = $conn->query("SELECT COUNT(*) AS c FROM held_invoices");
        $c = $q ? $q->fetch_assoc()['c'] : 0;
      ?>
      <h3><?= $c ?></h3>
    </div>
  </div>
  <div class="col-md-3">
    <div class="card p-3 shadow-sm">
      <h6 class="text-muted">العملاء</h6>
      <?php
        $q = $conn->query("SELECT COUNT(*) AS c FROM customers");
        $c = $q ? $q->fetch_assoc()['c'] : 0;
      ?>
      <h3><?= $c ?></h3>
    </div>
  </div>
</div>
<?php include("../includes/footer.php"); ?>
