<?php
// إعداد قاعدة البيانات تلقائياً
$host = "localhost";
$user = "root";
$db = "pos_system";

// قراءة كلمة المرور من ملف الإعدادات إذا كان موجوداً
$pass = "";
if (file_exists('config/db.php')) {
    $config_content = file_get_contents('config/db.php');
    if (preg_match('/\$pass\s*=\s*"([^"]*)"/', $config_content, $matches)) {
        $pass = $matches[1];
    }
}

echo "<h2>إعداد قاعدة البيانات</h2>";

try {
    // قراءة ملف SQL
    $sql_file = file_get_contents('sql/schema.sql');
    
    if (!$sql_file) {
        echo "<p style='color: red;'>لا يمكن قراءة ملف schema.sql</p>";
        exit;
    }
    
    // الاتصال بـ MySQL
    $conn = new mysqli($host, $user, $pass);
    
    if ($conn->connect_error) {
        echo "<p style='color: red;'>فشل الاتصال بـ MySQL: " . $conn->connect_error . "</p>";
        exit;
    }
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_file);
    
    echo "<p>تنفيذ الاستعلامات...</p>";
    
    foreach ($queries as $query) {
        $query = trim($query);
        if (!empty($query) && !preg_match('/^--/', $query)) {
            if ($conn->query($query) === TRUE) {
                echo "<p style='color: green;'>✓ تم تنفيذ: " . substr($query, 0, 50) . "...</p>";
            } else {
                echo "<p style='color: red;'>✗ خطأ في: " . substr($query, 0, 50) . "... - " . $conn->error . "</p>";
            }
        }
    }
    
    echo "<h3 style='color: green;'>تم إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
    echo "<ul>";
    echo "<li>اسم المستخدم: <strong>admin</strong></li>";
    echo "<li>كلمة المرور: <strong>admin123</strong></li>";
    echo "</ul>";
    echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>خطأ: " . $e->getMessage() . "</p>";
}
?>
