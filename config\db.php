<?php
$host = "localhost";
$user = "root";
$db   = "pos_system";

// محاولة كلمات مرور مختلفة
$passwords = ["", "root", "123456", "password", "admin", "mysql"];
$conn = null;

foreach ($passwords as $pass) {
    try {
        $test_conn = new mysqli($host, $user, $pass, $db);
        if (!$test_conn->connect_error) {
            $conn = $test_conn;
            break;
        }
    } catch (Exception $e) {
        continue;
    }
}

if (!$conn || $conn->connect_error) {
    // إذا فشل الاتصال بقاعدة البيانات، جرب بدون اسم قاعدة البيانات
    foreach ($passwords as $pass) {
        try {
            $test_conn = new mysqli($host, $user, $pass);
            if (!$test_conn->connect_error) {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                $test_conn->query("CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $test_conn->select_db('pos_system');
                $conn = $test_conn;
                break;
            }
        } catch (Exception $e) {
            continue;
        }
    }
}

if (!$conn || $conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات. يرجى التحقق من إعدادات MySQL في XAMPP.");
}
?>
