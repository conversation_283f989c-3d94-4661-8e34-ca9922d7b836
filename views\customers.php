
<?php include("../includes/header.php"); include("../config/db.php"); ?>
<div class="card p-3 shadow-sm">
  <div class="d-flex justify-content-between align-items-center mb-2">
    <h5>العملاء</h5>
    <form class="d-flex" method="post">
      <input name="name" class="form-control me-2" placeholder="اسم العميل" required>
      <input name="phone" class="form-control me-2" placeholder="جوال">
      <input name="email" class="form-control me-2" placeholder="بريد">
      <button class="btn btn-primary" name="add">إضافة</button>
    </form>
  </div>
  <?php
    if(isset($_POST['add'])){
        $name=$conn->real_escape_string($_POST['name']); $phone=$conn->real_escape_string($_POST['phone']); $email=$conn->real_escape_string($_POST['email']);
        $conn->query("INSERT INTO customers(name,phone,email) VALUES('$name','$phone','$email')");
        echo "<div class='alert alert-success'>تم الإضافة</div>";
    }
  ?>
  <div class="table-responsive">
    <table class="table table-bordered">
      <thead class="table-light"><tr><th>#</th><th>الاسم</th><th>الجوال</th><th>البريد</th><th>الرصيد</th></tr></thead>
      <tbody>
        <?php
          $q=$conn->query("SELECT * FROM customers ORDER BY id DESC");
          while($r=$q->fetch_assoc()){
            echo "<tr><td>{$r['id']}</td><td>{$r['name']}</td><td>{$r['phone']}</td><td>{$r['email']}</td><td>{$r['balance']}</td></tr>";
          }
        ?>
      </tbody>
    </table>
  </div>
</div>
<?php include("../includes/footer.php"); ?>
