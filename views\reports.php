
<?php include("../includes/header.php"); include("../config/db.php"); ?>
<div class="card p-3 shadow-sm">
  <h5>تقارير المبيعات</h5>
  <form class="row g-2 mb-3" method="get">
    <div class="col-auto">
      <label class="form-label">من</label>
      <input type="date" name="from" class="form-control" value="<?= htmlspecialchars($_GET['from'] ?? '') ?>">
    </div>
    <div class="col-auto">
      <label class="form-label">إلى</label>
      <input type="date" name="to" class="form-control" value="<?= htmlspecialchars($_GET['to'] ?? '') ?>">
    </div>
    <div class="col-auto align-self-end">
      <button class="btn btn-primary">تصفية</button>
    </div>
  </form>
  <?php
    $cond = "1=1";
    if(!empty($_GET['from'])){ $f=$conn->real_escape_string($_GET['from']); $cond .= " AND DATE(created_at) >= '$f'"; }
    if(!empty($_GET['to'])){ $t=$conn->real_escape_string($_GET['to']); $cond .= " AND DATE(created_at) <= '$t'"; }
    $q = $conn->query("SELECT * FROM sales WHERE $cond ORDER BY id DESC");
  ?>
  <div class="table-responsive">
    <table class="table table-bordered">
      <thead class="table-light"><tr><th>#</th><th>العميل</th><th>المجموع</th><th>الدفع</th><th>التاريخ</th></tr></thead>
      <tbody>
        <?php while($r=$q->fetch_assoc()): ?>
          <tr>
            <td><?= $r['id'] ?></td>
            <td><?= $r['customer_id'] ?></td>
            <td><?= number_format($r['total'],2) ?></td>
            <td><?= $r['payment_type'] === 'cash' ? 'نقدي' : 'آجل' ?></td>
            <td><?= $r['created_at'] ?></td>
          </tr>
        <?php endwhile; ?>
      </tbody>
    </table>
  </div>
</div>
<?php include("../includes/footer.php"); ?>
