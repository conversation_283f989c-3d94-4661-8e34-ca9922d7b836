<?php
echo "<h2>إصلاح مشكلة MySQL</h2>";

// كلمات المرور المحتملة
$passwords = ["", "root", "123456", "password", "admin", "mysql", "xampp"];
$host = "localhost";
$user = "root";

echo "<h3>اختبار كلمات المرور المختلفة:</h3>";

$working_password = null;

foreach ($passwords as $pass) {
    echo "<p>اختبار كلمة المرور: <strong>" . ($pass == "" ? "(فارغة)" : $pass) . "</strong> ... ";
    
    try {
        $conn = @new mysqli($host, $user, $pass);
        
        if (!$conn->connect_error) {
            echo "<span style='color: green;'>✓ نجحت!</span></p>";
            $working_password = $pass;
            break;
        } else {
            echo "<span style='color: red;'>✗ فشلت</span></p>";
        }
        $conn->close();
    } catch (Exception $e) {
        echo "<span style='color: red;'>✗ خطأ</span></p>";
    }
}

if ($working_password !== null) {
    echo "<h3 style='color: green;'>🎉 تم العثور على كلمة المرور الصحيحة!</h3>";
    echo "<p><strong>كلمة المرور هي:</strong> " . ($working_password == "" ? "(فارغة)" : $working_password) . "</p>";
    
    // تحديث ملف الإعدادات
    $config_content = "<?php\n";
    $config_content .= "\$host = \"localhost\";\n";
    $config_content .= "\$user = \"root\";\n";
    $config_content .= "\$pass = \"$working_password\";\n";
    $config_content .= "\$db   = \"pos_system\";\n\n";
    $config_content .= "\$conn = new mysqli(\$host, \$user, \$pass, \$db);\n\n";
    $config_content .= "if (\$conn->connect_error) {\n";
    $config_content .= "    die(\"فشل الاتصال بقاعدة البيانات: \" . \$conn->connect_error);\n";
    $config_content .= "}\n";
    $config_content .= "?>\n";
    
    if (file_put_contents('config/db.php', $config_content)) {
        echo "<p style='color: green;'>✓ تم تحديث ملف الإعدادات بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>✗ فشل في تحديث ملف الإعدادات</p>";
    }
    
    // إنشاء قاعدة البيانات
    try {
        $conn = new mysqli($host, $user, $working_password);
        
        echo "<h3>إنشاء قاعدة البيانات:</h3>";
        $sql = "CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات pos_system</p>";
        } else {
            echo "<p style='color: orange;'>قاعدة البيانات موجودة مسبقاً</p>";
        }
        
        // تنفيذ ملف SQL
        echo "<h3>إعداد الجداول:</h3>";
        $conn->select_db('pos_system');
        
        $sql_file = file_get_contents('sql/schema.sql');
        if ($sql_file) {
            // إزالة التعليقات والأسطر الفارغة
            $queries = array_filter(
                array_map('trim', explode(';', $sql_file)),
                function($query) {
                    return !empty($query) && !preg_match('/^--/', $query) && !preg_match('/^\/\*/', $query);
                }
            );
            
            $success_count = 0;
            $total_count = count($queries);
            
            foreach ($queries as $query) {
                if ($conn->query($query) === TRUE) {
                    $success_count++;
                    echo "<p style='color: green;'>✓ " . substr($query, 0, 50) . "...</p>";
                } else {
                    echo "<p style='color: red;'>✗ خطأ في: " . substr($query, 0, 50) . "... - " . $conn->error . "</p>";
                }
            }
            
            echo "<h3 style='color: green;'>تم تنفيذ $success_count من $total_count استعلام بنجاح!</h3>";
            
            if ($success_count > 0) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>🎉 تم إعداد النظام بنجاح!</h4>";
                echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
                echo "<ul>";
                echo "<li>اسم المستخدم: <strong>admin</strong></li>";
                echo "<li>كلمة المرور: <strong>admin123</strong></li>";
                echo "</ul>";
                echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚀 دخول النظام</a></p>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>✗ لا يمكن قراءة ملف schema.sql</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>خطأ في إعداد قاعدة البيانات: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<h3 style='color: red;'>❌ لم يتم العثور على كلمة مرور صحيحة</h3>";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h4>الحلول المقترحة:</h4>";
    echo "<ol>";
    echo "<li><strong>إعادة تشغيل XAMPP:</strong> أغلق XAMPP وافتحه مرة أخرى كمدير (Run as Administrator)</li>";
    echo "<li><strong>إعادة تعيين كلمة مرور MySQL:</strong>";
    echo "<ul>";
    echo "<li>افتح XAMPP Control Panel</li>";
    echo "<li>اضغط على 'Stop' بجانب MySQL</li>";
    echo "<li>اضغط على 'Config' بجانب MySQL واختر 'my.ini'</li>";
    echo "<li>أضف السطر التالي تحت [mysqld]: <code>skip-grant-tables</code></li>";
    echo "<li>احفظ الملف وأعد تشغيل MySQL</li>";
    echo "</ul></li>";
    echo "<li><strong>استخدام phpMyAdmin:</strong> اذهب إلى <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "</ol>";
    echo "</div>";
}
?>
