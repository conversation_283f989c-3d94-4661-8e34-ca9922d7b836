
<?php include("../config/db.php"); if (session_status()===PHP_SESSION_NONE) session_start(); if(isset($_SESSION['user_id'])){ header("Location: dashboard.php"); exit; } ?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <title>تسجيل الدخول</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
</head>
<body class="bg-light d-flex justify-content-center align-items-center vh-100">
  <div class="card p-4 shadow-lg" style="width:400px;">
    <h3 class="text-center mb-3">تسجيل الدخول</h3>
    <form method="post" action="../actions/login_action.php">
      <div class="mb-3">
        <label class="form-label">اسم المستخدم</label>
        <input type="text" name="username" class="form-control" required>
      </div>
      <div class="mb-3">
        <label class="form-label">كلمة المرور</label>
        <input type="password" name="password" class="form-control" required>
      </div>
      <button type="submit" class="btn btn-primary w-100">دخول</button>
      <p class="mt-3 small text-muted">بيانات افتراضية: المستخدم <b>admin</b> كلمة السر <b>admin123</b></p>
    </form>
  </div>
</body>
</html>
