<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - نقطة البيع</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .btn { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 10px 5px; }
        .btn:hover { background: #005a87; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعداد نظام نقطة البيع</h1>
        
        <div class="error">
            <h3>❌ مشكلة في الاتصال بقاعدة البيانات</h3>
            <p>يبدو أن MySQL يحتاج كلمة مرور. دعنا نحل هذه المشكلة خطوة بخطوة.</p>
        </div>

        <h3>🔍 اختبار كلمات المرور:</h3>
        
        <?php
        $passwords = ["", "root", "123456", "password", "admin", "mysql", "xampp"];
        $host = "localhost";
        $user = "root";
        $working_password = null;

        foreach ($passwords as $pass) {
            echo "<p>اختبار كلمة المرور: <strong>" . ($pass == "" ? "(فارغة)" : $pass) . "</strong> ... ";
            
            try {
                $conn = @new mysqli($host, $user, $pass);
                
                if (!$conn->connect_error) {
                    echo "<span style='color: green;'>✓ نجحت!</span></p>";
                    $working_password = $pass;
                    break;
                } else {
                    echo "<span style='color: red;'>✗ فشلت</span></p>";
                }
                if ($conn) $conn->close();
            } catch (Exception $e) {
                echo "<span style='color: red;'>✗ خطأ</span></p>";
            }
        }

        if ($working_password !== null) {
            echo "<div class='success'>";
            echo "<h3>🎉 تم العثور على كلمة المرور الصحيحة!</h3>";
            echo "<p><strong>كلمة المرور:</strong> " . ($working_password == "" ? "(فارغة)" : $working_password) . "</p>";
            
            // تحديث ملف الإعدادات
            $config_content = "<?php\n";
            $config_content .= "\$host = \"localhost\";\n";
            $config_content .= "\$user = \"root\";\n";
            $config_content .= "\$pass = \"$working_password\";\n";
            $config_content .= "\$db   = \"pos_system\";\n\n";
            $config_content .= "\$conn = new mysqli(\$host, \$user, \$pass, \$db);\n\n";
            $config_content .= "if (\$conn->connect_error) {\n";
            $config_content .= "    die(\"فشل الاتصال بقاعدة البيانات: \" . \$conn->connect_error);\n";
            $config_content .= "}\n";
            $config_content .= "?>\n";
            
            if (file_put_contents('config/db.php', $config_content)) {
                echo "<p style='color: green;'>✓ تم تحديث ملف الإعدادات!</p>";
                
                // إنشاء قاعدة البيانات والجداول
                try {
                    $conn = new mysqli($host, $user, $working_password);
                    
                    // إنشاء قاعدة البيانات
                    $sql = "CREATE DATABASE IF NOT EXISTS pos_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
                    $conn->query($sql);
                    
                    // الاتصال بقاعدة البيانات
                    $conn->select_db('pos_system');
                    
                    // قراءة وتنفيذ ملف SQL
                    if (file_exists('sql/schema.sql')) {
                        $sql_content = file_get_contents('sql/schema.sql');
                        $queries = explode(';', $sql_content);
                        
                        $success_count = 0;
                        foreach ($queries as $query) {
                            $query = trim($query);
                            if (!empty($query) && !preg_match('/^--/', $query)) {
                                if ($conn->query($query)) {
                                    $success_count++;
                                }
                            }
                        }
                        
                        if ($success_count > 0) {
                            echo "<p style='color: green;'>✓ تم إعداد قاعدة البيانات والجداول!</p>";
                            echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                            echo "<h4>🚀 النظام جاهز للاستخدام!</h4>";
                            echo "<p><strong>بيانات تسجيل الدخول:</strong></p>";
                            echo "<ul><li>اسم المستخدم: <strong>admin</strong></li>";
                            echo "<li>كلمة المرور: <strong>admin123</strong></li></ul>";
                            echo "<a href='views/login.php' class='btn'>دخول النظام الآن</a>";
                            echo "</div>";
                        }
                    }
                    
                } catch (Exception $e) {
                    echo "<p style='color: red;'>خطأ في إعداد قاعدة البيانات: " . $e->getMessage() . "</p>";
                }
            }
            echo "</div>";
            
        } else {
            echo "<div class='error'>";
            echo "<h3>❌ لم يتم العثور على كلمة مرور صحيحة</h3>";
            echo "<p>جرب الحلول التالية:</p>";
            echo "<ol>";
            echo "<li><strong>إعادة تشغيل XAMPP:</strong> أغلق XAMPP وافتحه كمدير (Run as Administrator)</li>";
            echo "<li><strong>استخدام phpMyAdmin:</strong> <a href='http://localhost/phpmyadmin' target='_blank' class='btn'>فتح phpMyAdmin</a></li>";
            echo "<li><strong>إعادة تعيين كلمة مرور MySQL</strong></li>";
            echo "</ol>";
            echo "</div>";
            
            echo "<div class='info'>";
            echo "<h4>📋 خطوات إعادة تعيين كلمة مرور MySQL:</h4>";
            echo "<ol>";
            echo "<li>افتح XAMPP Control Panel كمدير</li>";
            echo "<li>اضغط 'Stop' بجانب MySQL</li>";
            echo "<li>اضغط 'Config' بجانب MySQL واختر 'my.ini'</li>";
            echo "<li>أضف هذا السطر تحت [mysqld]:<br><code>skip-grant-tables</code></li>";
            echo "<li>احفظ الملف وأعد تشغيل MySQL</li>";
            echo "<li>ارجع لهذه الصفحة</li>";
            echo "</ol>";
            echo "</div>";
        }
        ?>
        
        <hr>
        <p><a href="temp_login.php" class="btn">إعادة المحاولة</a></p>
    </div>
</body>
</html>
