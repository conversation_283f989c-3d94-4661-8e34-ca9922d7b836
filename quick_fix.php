<?php
// إصلاح سريع لمشكلة كلمة المرور
$passwords = ["", "root", "123456", "password", "admin", "mysql"];
$host = "localhost";
$user = "root";

echo "<h2>إصلاح سريع لكلمة مرور MySQL</h2>";

foreach ($passwords as $pass) {
    try {
        $conn = @new mysqli($host, $user, $pass);
        if (!$conn->connect_error) {
            echo "<p style='color: green;'>✓ كلمة المرور الصحيحة: " . ($pass == "" ? "(فارغة)" : $pass) . "</p>";
            
            // تحديث ملف config/db.php
            $new_config = '<?php
$host = "localhost";
$user = "root";
$pass = "' . $pass . '";
$db   = "pos_system";

$conn = new mysqli($host, $user, $pass, $db);

if ($conn->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
}
?>';
            
            if (file_put_contents('config/db.php', $new_config)) {
                echo "<p style='color: green;'>✓ تم تحديث ملف الإعدادات!</p>";
                echo "<p><a href='views/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>دخول النظام الآن</a></p>";
                echo "<hr>";
                echo "<h3>بيانات تسجيل الدخول:</h3>";
                echo "<p><strong>اسم المستخدم:</strong> admin</p>";
                echo "<p><strong>كلمة المرور:</strong> admin123</p>";
            } else {
                echo "<p style='color: red;'>✗ فشل في تحديث ملف الإعدادات</p>";
            }
            break;
        }
    } catch (Exception $e) {
        // تجاهل الأخطاء والمتابعة
    }
}
?>
