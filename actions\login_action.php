
<?php
include("../config/db.php");
session_start();

$username = $conn->real_escape_string($_POST['username'] ?? '');
$password = $_POST['password'] ?? '';

$sql = "SELECT * FROM users WHERE username='$username' LIMIT 1";
$res = $conn->query($sql);
if ($res && $res->num_rows === 1) {
    $user = $res->fetch_assoc();
    if (password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['role'] = $user['role'];
        header("Location: ../views/dashboard.php"); exit;
    }
}
echo "❌ اسم المستخدم أو كلمة المرور غير صحيحة. <a href='../views/login.php'>رجوع</a>";
