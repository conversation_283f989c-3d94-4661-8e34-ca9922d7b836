
<?php include("../includes/header.php"); include("../config/db.php"); ?>
<div class="card p-3 shadow-sm">
  <h5 class="mb-3">الفواتير المعلقة</h5>
  <div class="table-responsive">
    <table class="table table-bordered">
      <thead class="table-light"><tr><th>#</th><th>العميل</th><th>ملاحظة</th><th>الإجمالي</th><th>التاريخ</th><th>استعادة</th><th>حذف</th></tr></thead>
      <tbody>
      <?php
        $q = $conn->query("SELECT h.*, IFNULL(c.name,'بدون') AS customer_name FROM held_invoices h LEFT JOIN customers c ON c.id=h.customer_id ORDER BY h.id DESC");
        while($r = $q->fetch_assoc()){
          echo "<tr>
            <td>{$r['id']}</td>
            <td>{$r['customer_name']}</td>
            <td>".htmlspecialchars($r['note'] ?? '')."</td>
            <td>{$r['total']}</td>
            <td>{$r['created_at']}</td>
            <td><a class='btn btn-sm btn-info' href='../actions/invoice_action.php?action=resume&id={$r['id']}'>استعادة</a></td>
            <td><a class='btn btn-sm btn-danger' href='../actions/invoice_action.php?action=delete&id={$r['id']}' onclick='return confirm("حذف؟")'>حذف</a></td>
          </tr>";
        }
      ?>
      </tbody>
    </table>
  </div>
</div>
<?php include("../includes/footer.php"); ?>
